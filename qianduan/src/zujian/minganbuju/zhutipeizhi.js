// 主题配置文件
// 定义明亮和暗黑两套主题的所有样式变量

// 明亮主题配置
export const minglia<PERSON><PERSON><PERSON><PERSON> = {
  // 基础颜色
  yanse: {
    // 主要颜色
    zhuyao: '#1976d2',
    zhuyao_qian: '#1565c0',
    zhuyao_hou: '#42a5f5',
    
    // 次要颜色
    ciyao: '#dc004e',
    ciyao_qian: '#c51162',
    ciyao_hou: '#f06292',
    
    // 背景颜色
    beijing: '#ffffff',
    beijing_er: '#f5f5f5',
    beijing_san: '#eeeeee',
    
    // 表面颜色
    biaomian: '#ffffff',
    biaomian_er: '#fafafa',
    
    // 文字颜色
    wenzi_zhuyao: '#212121',
    wenzi_ciyao: '#757575',
    wenzi_jinzhi: '#bdbdbd',
    wenzi_tishi: '#9e9e9e',
    
    // 边框颜色
    biankuang: '#e0e0e0',
    biankuang_qian: '#bdbdbd',
    
    // 状态颜色
    chenggong: '#4caf50',
    jinggao: '#ff9800',
    cuowu: '#f44336',
    xinxi: '#2196f3',
    
    // 阴影颜色
    yinying: 'rgba(0, 0, 0, 0.12)',
    yinying_qian: 'rgba(0, 0, 0, 0.24)',
  },
  
  // 字体配置
  ziti: {
    jiazu: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    daxiao: {
      xiaoxiao: '12px',
      xiao: '14px',
      zhongdeng: '16px',
      da: '18px',
      dada: '24px',
      chaoda: '32px',
    },
    zhongliang: {
      xichang: 300,
      putong: 400,
      zhongdeng: 500,
      cuhei: 600,
      hei: 700,
    },
    xinggao: {
      jinmi: 1.2,
      putong: 1.5,
      kuansong: 1.8,
    },
  },
  
  // 间距配置
  jianju: {
    xiaoxiao: '4px',
    xiao: '8px',
    zhongdeng: '16px',
    da: '24px',
    dada: '32px',
    chaoda: '48px',
  },
  
  // 圆角配置
  yuanjiao: {
    xiao: '4px',
    zhongdeng: '8px',
    da: '12px',
    yuan: '50%',
  },
  
  // 过渡动画配置
  donghua: {
    sujian: {
      kuai: '0.15s',
      zhongdeng: '0.3s',
      man: '0.5s',
    },
    huanman: {
      jinru: 'cubic-bezier(0.4, 0, 0.2, 1)',
      tuichu: 'cubic-bezier(0.4, 0, 1, 1)',
      biaozhun: 'cubic-bezier(0.4, 0, 0.6, 1)',
    },
  },
  
  // 阴影配置
  yinying: {
    xiao: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
    zhongdeng: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
    da: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
    chaoda: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
  },
};

// 暗黑主题配置
export const anheizuti = {
  // 基础颜色
  yanse: {
    // 主要颜色
    zhuyao: '#90caf9',
    zhuyao_qian: '#64b5f6',
    zhuyao_hou: '#bbdefb',
    
    // 次要颜色
    ciyao: '#f48fb1',
    ciyao_qian: '#f06292',
    ciyao_hou: '#ffc1cc',
    
    // 极光风格背景颜色
    beijing: 'linear-gradient(135deg, #0c0c0c 0%, #1a0d2e 25%, #16213e 50%, #0f3460 75%, #0c0c0c 100%)',
    beijing_er: 'linear-gradient(45deg, #1e1e1e 0%, #2d1b3d 30%, #1e3a5f 60%, #1e1e1e 100%)',
    beijing_san: 'linear-gradient(90deg, #2d2d2d 0%, #3d2a4a 50%, #2d2d2d 100%)',

    // 极光辅助背景（用于动画效果）
    jiguang_beijing_1: 'radial-gradient(ellipse at top, rgba(138, 43, 226, 0.15) 0%, transparent 50%)',
    jiguang_beijing_2: 'radial-gradient(ellipse at bottom left, rgba(0, 255, 127, 0.1) 0%, transparent 50%)',
    jiguang_beijing_3: 'radial-gradient(ellipse at bottom right, rgba(30, 144, 255, 0.1) 0%, transparent 50%)',
    jiguang_beijing_4: 'radial-gradient(ellipse at center, rgba(255, 20, 147, 0.08) 0%, transparent 60%)',

    // 表面颜色（带极光效果）
    biaomian: 'linear-gradient(135deg, rgba(30, 30, 30, 0.9) 0%, rgba(61, 42, 74, 0.8) 50%, rgba(30, 30, 30, 0.9) 100%)',
    biaomian_er: 'linear-gradient(45deg, rgba(45, 45, 45, 0.9) 0%, rgba(75, 52, 94, 0.8) 50%, rgba(45, 45, 45, 0.9) 100%)',
    
    // 文字颜色
    wenzi_zhuyao: '#ffffff',
    wenzi_ciyao: '#b3b3b3',
    wenzi_jinzhi: '#666666',
    wenzi_tishi: '#999999',
    
    // 边框颜色
    biankuang: '#404040',
    biankuang_qian: '#666666',
    
    // 状态颜色
    chenggong: '#66bb6a',
    jinggao: '#ffb74d',
    cuowu: '#ef5350',
    xinxi: '#42a5f5',
    
    // 阴影颜色
    yinying: 'rgba(0, 0, 0, 0.5)',
    yinying_qian: 'rgba(0, 0, 0, 0.8)',
  },
  
  // 字体配置（与明亮主题相同）
  ziti: mingliangzhuti.ziti,
  
  // 间距配置（与明亮主题相同）
  jianju: mingliangzhuti.jianju,
  
  // 圆角配置（与明亮主题相同）
  yuanjiao: mingliangzhuti.yuanjiao,
  
  // 过渡动画配置（与明亮主题相同）
  donghua: mingliangzhuti.donghua,
  
  // 阴影配置（暗黑主题专用）
  yinying: {
    xiao: '0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.6)',
    zhongdeng: '0 3px 6px rgba(0, 0, 0, 0.6), 0 3px 6px rgba(0, 0, 0, 0.7)',
    da: '0 10px 20px rgba(0, 0, 0, 0.7), 0 6px 6px rgba(0, 0, 0, 0.8)',
    chaoda: '0 14px 28px rgba(0, 0, 0, 0.8), 0 10px 10px rgba(0, 0, 0, 0.9)',
  },
};

// 主题类型枚举
export const zhutileixing = {
  mingliang: 'mingliang',
  anhei: 'anhei',
};

// 默认主题
export const morenzuti = mingliangzhuti;

// 主题映射
export const zhutiyingshe = {
  [zhutileixing.mingliang]: mingliangzhuti,
  [zhutileixing.anhei]: anheizuti,
};

// 获取主题函数
export const huoquzhuti = (zhutimingcheng) => {
  return zhutiyingshe[zhutimingcheng] || morenzuti;
};
