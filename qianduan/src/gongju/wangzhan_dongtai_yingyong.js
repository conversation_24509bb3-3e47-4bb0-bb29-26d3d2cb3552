import cuowurizhi from './chushihua_cuowurizhi.js';

/**
 * 网站动态应用模块
 */
function shezhi_wangzhan_biaoti(wangzhan_mingcheng) {
  try {
    if (!wangzhan_mingcheng || typeof wangzhan_mingcheng !== 'string') {
      return false;
    }
    document.title = wangzhan_mingcheng;
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.shezhi_wangzhan_biaoti_shibai, cuowu);
    return false;
  }
}

function shezhi_wangzhan_tubiao(tubiao_lianjie) {
  try {
    if (!tubiao_lianjie || typeof tubiao_lianjie !== 'string') {
      return false;
    }
    let favicon_yuansu = document.querySelector('link[rel="icon"]') ||
      document.querySelector('link[rel="shortcut icon"]') ||
      document.querySelector('link[rel="apple-touch-icon"]');
    if (!favicon_yuansu) {
      favicon_yuansu = document.createElement('link');
      favicon_yuansu.rel = 'icon';
      favicon_yuansu.type = 'image/x-icon';
      document.head.appendChild(favicon_yuansu);
    }
    favicon_yuansu.href = tubiao_lianjie;
    return true;
  } catch (cuowu) {
    console.error(cuowurizhi.shezhi_wangzhan_tubiao_shibai, cuowu);
    return false;
  }
}

function yingyong_wangzhan_jichuxinxi(wangzhan_xinxi) {
  const yingyong_jieguo = {
    chenggong_shuliang: 0,
    shibai_shuliang: 0,
    xiangxi_jieguo: {}
  };
  try {
    if (!wangzhan_xinxi || typeof wangzhan_xinxi !== 'object') {
      console.error(cuowurizhi.wangzhan_jichuxinxi_shuju_wuxiao);
      return yingyong_jieguo;
    }
    if (wangzhan_xinxi.wangzhan_mingcheng) {
      const biaoti_jieguo = shezhi_wangzhan_biaoti(wangzhan_xinxi.wangzhan_mingcheng);
      yingyong_jieguo.xiangxi_jieguo.biaoti = biaoti_jieguo;
      if (biaoti_jieguo) {
        yingyong_jieguo.chenggong_shuliang++;
      } else {
        yingyong_jieguo.shibai_shuliang++;
      }
    }
    if (wangzhan_xinxi.wangzhan_tubiao_lianjie) {
      const tubiao_jieguo = shezhi_wangzhan_tubiao(wangzhan_xinxi.wangzhan_tubiao_lianjie);
      yingyong_jieguo.xiangxi_jieguo.tubiao = tubiao_jieguo;
      if (tubiao_jieguo) {
        yingyong_jieguo.chenggong_shuliang++;
      } else {
        yingyong_jieguo.shibai_shuliang++;
      }
    }
    return yingyong_jieguo;
  } catch (cuowu) {
    console.error(cuowurizhi.dongtai_yingyong_wangzhan_jichuxinxi_shibai, cuowu);
    yingyong_jieguo.shibai_shuliang++;
    return yingyong_jieguo;
  }
}

function kuaisu_yingyong_mingcheng_tubiao(wangzhan_xinxi) {
  try {
    if (!wangzhan_xinxi || typeof wangzhan_xinxi !== 'object') {
      console.error(cuowurizhi.wangzhan_jichuxinxi_shuju_wuxiao);
      return false;
    }
    let chenggong_biaozhi = true;
    if (wangzhan_xinxi.wangzhan_mingcheng) {
      if (!shezhi_wangzhan_biaoti(wangzhan_xinxi.wangzhan_mingcheng)) {
        chenggong_biaozhi = false;
      }
    }
    if (wangzhan_xinxi.wangzhan_tubiao_lianjie) {
      if (!shezhi_wangzhan_tubiao(wangzhan_xinxi.wangzhan_tubiao_lianjie)) {
        chenggong_biaozhi = false;
      }
    }
    return chenggong_biaozhi;
  } catch (cuowu) {
    console.error(cuowurizhi.kuaisu_yingyong_mingcheng_tubiao_shibai, cuowu);
    return false;
  }
}

export {
  shezhi_wangzhan_biaoti,
  shezhi_wangzhan_tubiao,
  yingyong_wangzhan_jichuxinxi,
  kuaisu_yingyong_mingcheng_tubiao
};
